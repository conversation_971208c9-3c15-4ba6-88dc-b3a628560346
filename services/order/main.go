package main

import (
    "context"
    "log"
    "net"

    "google.golang.org/grpc"
    "google.golang.org/grpc/credentials/insecure"
    pb "learn-go-demo/proto"
    "learn-go-demo/pkg/discovery"
)

type OrderServer struct {
    pb.UnimplementedOrderServiceServer
    userClient pb.UserServiceClient
}

func (s *OrderServer) CreateOrder(ctx context.Context, req *pb.CreateOrderRequest) (*pb.CreateOrderResponse, error) {
    // 调用用户服务验证用户
    userResp, err := s.userClient.GetUser(ctx, &pb.GetUserRequest{Id: req.UserId})
    if err != nil {
        return nil, err
    }

    log.Printf("Creating order for user: %s", userResp.Name)

    return &pb.CreateOrderResponse{
        OrderId: 456,
        Message: "Order created successfully",
    }, nil
}

func (s *OrderServer) GetOrder(ctx context.Context, req *pb.GetOrderRequest) (*pb.GetOrderResponse, error) {
    return &pb.GetOrderResponse{
        OrderId: req.OrderId,
        UserId:  1,
        Product: "Sample Product",
        Amount:  99.99,
    }, nil
}

func main() {
    registry := discovery.NewServiceRegistry()
    
    // 发现用户服务
    userAddrs, err := registry.Discover("user-service")
    if err != nil || len(userAddrs) == 0 {
        log.Fatalf("Failed to discover user service: %v", err)
    }

    // 连接用户服务
    conn, err := grpc.Dial(userAddrs[0], grpc.WithTransportCredentials(insecure.NewCredentials()))
    if err != nil {
        log.Fatalf("Failed to connect to user service: %v", err)
    }
    defer conn.Close()

    userClient := pb.NewUserServiceClient(conn)

    lis, err := net.Listen("tcp", ":50052")
    if err != nil {
        log.Fatalf("Failed to listen: %v", err)
    }

    err = registry.Register("order-service", "localhost:50052", 30)
    if err != nil {
        log.Fatalf("Failed to register service: %v", err)
    }

    s := grpc.NewServer()
    pb.RegisterOrderServiceServer(s, &OrderServer{userClient: userClient})

    log.Println("Order service starting on :50052")
    if err := s.Serve(lis); err != nil {
        log.Fatalf("Failed to serve: %v", err)
    }
}