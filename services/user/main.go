package main

import (
    "context"
    "log"
    "net"

    "google.golang.org/grpc"
    pb "learn-go-demo/proto"
    "learn-go-demo/pkg/discovery"
)

type UserServer struct {
    pb.UnimplementedUserServiceServer
}

func (s *UserServer) GetUser(ctx context.Context, req *pb.GetUserRequest) (*pb.GetUserResponse, error) {
    return &pb.GetUserResponse{
        Id:    req.Id,
        Name:  "<PERSON> Doe",
        Email: "<EMAIL>",
    }, nil
}

func (s *UserServer) CreateUser(ctx context.Context, req *pb.CreateUserRequest) (*pb.CreateUserResponse, error) {
    return &pb.CreateUserResponse{
        Id:      123,
        Message: "User created successfully",
    }, nil
}

func main() {
    lis, err := net.Listen("tcp", ":50051")
    if err != nil {
        log.Fatalf("Failed to listen: %v", err)
    }

    registry := discovery.NewServiceRegistry()
    err = registry.Register("user-service", "localhost:50051", 30)
    if err != nil {
        log.Fatalf("Failed to register service: %v", err)
    }

    s := grpc.NewServer()
    pb.RegisterUserServiceServer(s, &UserServer{})

    log.Println("User service starting on :50051")
    if err := s.Serve(lis); err != nil {
        log.Fatalf("Failed to serve: %v", err)
    }
}