package main

import (
    "context"
    "log"

    "google.golang.org/grpc"
    "google.golang.org/grpc/credentials/insecure"
    pb "learn-go-demo/proto"
    "learn-go-demo/pkg/discovery"
)

func main() {
    registry := discovery.NewServiceRegistry()

    // 测试订单服务
    orderAddrs, err := registry.Discover("order-service")
    if err != nil || len(orderAddrs) == 0 {
        log.Fatalf("Failed to discover order service: %v", err)
    }

    conn, err := grpc.Dial(orderAddrs[0], grpc.WithTransportCredentials(insecure.NewCredentials()))
    if err != nil {
        log.Fatalf("Failed to connect: %v", err)
    }
    defer conn.Close()

    client := pb.NewOrderServiceClient(conn)

    resp, err := client.CreateOrder(context.Background(), &pb.CreateOrderRequest{
        UserId:  1,
        Product: "Test Product",
        Amount:  100.0,
    })
    if err != nil {
        log.Fatalf("CreateOrder failed: %v", err)
    }

    log.Printf("Order created: %v", resp)
}