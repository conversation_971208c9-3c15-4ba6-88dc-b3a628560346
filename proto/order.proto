syntax = "proto3";

package order;
option go_package = "./proto";

service OrderService {
  rpc CreateOrder(CreateOrderRequest) returns (CreateOrderResponse);
  rpc GetOrder(GetOrderRequest) returns (GetOrderResponse);
}

message CreateOrderRequest {
  int32 user_id = 1;
  string product = 2;
  double amount = 3;
}

message CreateOrderResponse {
  int32 order_id = 1;
  string message = 2;
}

message GetOrderRequest {
  int32 order_id = 1;
}

message GetOrderResponse {
  int32 order_id = 1;
  int32 user_id = 2;
  string product = 3;
  double amount = 4;
}